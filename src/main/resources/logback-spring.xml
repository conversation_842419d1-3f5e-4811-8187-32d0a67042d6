<configuration scan="true">
    <jmxConfigurator />
    <springProfile name="aws">
        <appender name="logstash-appender" class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="net.logstash.logback.encoder.LogstashEncoder">
                <fieldNames>
                    <timestamp>time</timestamp>
                    <message>msg</message>
                    <level>level_desc</level>
                    <levelValue>level</levelValue>
                    <stackTrace>stack</stackTrace>
                </fieldNames>
                <includeMdc>true</includeMdc>
                <timeZone>UTC</timeZone>
            </encoder>
        </appender>

        <root level="info">
            <appender-ref ref="logstash-appender"/>
        </root>
    </springProfile>

    <springProfile name="!aws">
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <layout class="ch.qos.logback.classic.PatternLayout">
                <Pattern>%d{HH:mm:ss.SSS} [%thread] [%X{session_id}] [%X{trace_id}/%X{span_id}] %-5level %logger{36} - %msg%n</Pattern>
            </layout>
        </appender>

        <root level="info">
            <appender-ref ref="STDOUT"/>
        </root>
    </springProfile>
</configuration>