spring:
  application:
    name: bo-gateway
  main:
    banner-mode: off
  security:
    oauth2:
      client:
        registration:
          keycloak:
            client-id: ${KEYCLOAK_CLIENT_ID:bo}
            client-secret: ${KEYCLOAK_CLIENT_SECRET:if_you_dont_know_ask_devops}
            redirect-uri: ${bo-gateway.base-uri}/${bo-gateway.proxyPathPrefix}/login/oauth2/code/keycloak
            authorization-grant-type: authorization_code
            scope: openid
        provider:
          keycloak:
            issuer-uri: ${keycloak.base-uri}/realms/${keycloak.realm}
            jwk-set-uri: ${keycloak.base-uri}/realms/${keycloak.realm}/protocol/openid-connect/certs
  reactor:
    context-propagation: auto

  cloud:
    gateway:
      httpclient:
        connect-timeout: ${DEFAULT_CONNECTION_TIMEOUT:5000}
  data:
    redis:
      host: ${REDIS_HOST:redis}
      port: ${REDIS_PORT:6379}

server:
  port: 3000

management:
  health:
    livenessstate.enabled: true
    readinessstate.enabled: true
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env,loggers,springdoc
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
    metrics:
      enabled: true
    loggers:
      enabled: true
  metrics:
    enable:
      all: false
      spring.component.calls: true
      http: true
      cache.size: true
    distribution:
      percentiles-histogram:
        all: false
      percentiles:
        all: 0.95, 0.99
  tracing:
    sampling:
      probability: 1.0
    baggage:
      correlation:
        fields:
          - sessionId
          - customerId
  datadog:
    metrics:
      export:
        api-key: ${DATADOG_API_KEY:********************************}
        enabled: ${DATADOG_ENABLED:false}
        host-tag: host

bo-gateway:
  base-uri: ${BO_GATEWAY_BASE_URI:http://local.skilling.com}
  proxyPathPrefix: ${PROXY_PATH_PREFIX:/bog}
  security:
    session:
      max-inactive-interval: ${SESSION_MAX_INACTIVE_INTERVAL:PT60M}
    connection-pool:
      max-life-time: ${CONNECTION_POOL_MAX_LIFE_TIME:PT1M}

keycloak:
  realm: ${KEYCLOAK_REALM:backoffice}
  base-uri: ${KEYCLOAK_PUBLIC_BASE_URI:http://id-staging-internal.skilling.com}

api:
  uris:
    auditServiceUri: http://${AUDIT_SERVICE_HOST:audit-service}${FQDN:.staging.internal}:${AUDIT_SERVICE_PORT:85}
    customerServiceUri: http://${CUSTOMER_SERVICE_HOST:customer-service}${FQDN:.staging.internal}:${CUSTOMER_SERVICE_PORT:82}
    elasticsearchUri: ${ELASTICSEARCH_URI:http://es-trd}${FQDN:.staging.internal}
    feedServiceUri: http://${FEED_SERVICE_HOST:feed-service}${FQDN:.staging.internal}:${FEED_SERVICE_PORT:80}
    kycServiceUri: http://${KYC_GATEWAY_HOST:kyc-gateway}${FQDN:.staging.internal}:${KYC_GATEWAY_PORT:98}
    paymentServiceUri: http://${PAYMENT_SERVICE_HOST:payment-service}${FQDN:.staging.internal}:${PAYMENT_SERVICE_PORT:80}
    reportingServiceUri: http://${REPORTING_SERVICE_HOST:reporting-service}${FQDN:.staging.internal}:${REPORTING_SERVICE_PORT:89}
    tradingAccountServiceUri: http://${TRADING_ACCOUNT_SERVICE_HOST:trading-account-service}${FQDN:.staging.internal}:${TRADING_ACCOUNT_SERVICE_PORT:90}
    tradingBalanceServiceUri: http://${TRADING_BALANCE_SERVICE_HOST:trading-balance-service}${FQDN:.staging.internal}:${TRADING_BALANCE_SERVICE_PORT:90}
    hasuraServiceUri: ${HASURA_GRAPHQL_URL:http://hasura.staging.internal/v1/graphql}
  timeouts:
    extendedSearchTimeout: ${EXTENDED_SEARCH_TIMEOUT:60000}

logging:
  traceable:
    http.mdc-cleaner-disabled: true
    reactive.enabled: true
  level:
    "org.springframework.security": ${LOG_LEVEL_SPRING_SECURITY:info}

springdoc:
  api-docs:
    enabled: true
    groups:
      enabled: true
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    oauth:
      client-id: ${KEYCLOAK_CLIENT_ID:bo}
      client-secret: ${KEYCLOAK_CLIENT_SECRET:if_you_dont_know_ask_devops}
  oauth-flow:
    authorizationUrl: ${keycloak.base-uri}/realms/${keycloak.realm}/protocol/openid-connect/auth
    tokenUrl: ${keycloak.base-uri}/realms/${keycloak.realm}/protocol/openid-connect/token

