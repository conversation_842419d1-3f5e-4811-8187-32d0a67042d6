package com.propero.bogateway.helpers;

import com.propero.bogateway.config.ApiProps;
import com.propero.bogateway.filters.factories.AuthFilterGatewayFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.stream.Stream;

import static com.google.common.base.MoreObjects.firstNonNull;
import static org.apache.commons.lang3.StringUtils.EMPTY;

@RequiredArgsConstructor
@Component
@Slf4j
public class SecurityHelper {
    private final AuthFilterGatewayFactory authFilterFactory;
    private final ApiProps gatewayProperties;

    public GatewayFilter authIfHasAnyRole(String... roles) {
        String[] normalizedRoles = normalizeRoles(roles).toArray(String[]::new);
        log.info("Checking if user has needed permissions");
        return authFilterFactory.apply(AuthFilterGatewayFactory.Config.withRoles(normalizedRoles));
    }

    private Stream<String> normalizeRoles(String... roles) {
        String rolePrefix = firstNonNull(gatewayProperties.getSecurity().getRolePrefix(), EMPTY);
        return Arrays.stream(roles).map(role -> rolePrefix + role);
    }
}
