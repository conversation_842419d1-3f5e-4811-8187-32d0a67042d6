
package com.propero.bogateway.filters.factories;

import com.propero.bogateway.config.AccessPermissions;
import com.propero.bogateway.filters.factories.AuthFilterGatewayFactory;
import com.propero.bogateway.filters.factories.AuthFilterGatewayFactory.Config;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextImpl;
import org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.security.web.server.context.SecurityContextServerWebExchange;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.mock.http.server.reactive.MockServerHttpRequest.get;

@ExtendWith(MockitoExtension.class)
class AuthFilterGatewayFactoryTest {

    @Mock
    private GatewayFilterChain filterChain;
    @Mock
    private OAuth2User oAuth2User;

    private AuthFilterGatewayFactory factory;

    @BeforeEach
    void setUp() {
        factory = new AuthFilterGatewayFactory();
        when(filterChain.filter(any())).thenReturn(Mono.empty());
    }

    @Test
    void rejectedIfNotLoggedIn() {
        // given
        GatewayFilter filter = factory.apply(confForRoles("authorizedRole1"));

        // when
        Mono<Void> result = filter.filter(MockServerWebExchange.from(get("/bog/v2/customers/1234")), filterChain);

        // then
        StepVerifier.create(result)
                .expectErrorMatches(err -> err instanceof InsufficientAuthenticationException)
                .verify();
    }

    @Test
    void rejectedIfNotPermitted() {
        // given
        GatewayFilter filter = factory.apply(confForRoles(AccessPermissions.WRITE_CUST));

        // when
        Mono<Void> result = filter.filter(exchange(AccessPermissions.READ_CUST), filterChain);  // note role doesn't match

        // then
        StepVerifier.create(result)
                .expectErrorMatches(err -> err instanceof ResponseStatusException)
                .verify();
    }

    @Test
    void okWhenLoggedInWithWhitelistedAuthorities() {
        // given
        GatewayFilter filter = factory.apply(confForRoles(AccessPermissions.READ_CUST, AccessPermissions.WRITE_CUST));

        // when
        Mono<Void> result = filter.filter(exchange(AccessPermissions.WRITE_CUST), filterChain);

        // then
        StepVerifier.create(result).verifyComplete();
    }

    private SecurityContextServerWebExchange exchange(String... authorities) {
        List<SimpleGrantedAuthority> grantedAuthorities = Stream.of(authorities).map(SimpleGrantedAuthority::new).toList();
        return new SecurityContextServerWebExchange(
                MockServerWebExchange.from(MockServerHttpRequest.get("/bog/endpoint")),
                Mono.just(new SecurityContextImpl(new OAuth2AuthenticationToken(oAuth2User, grantedAuthorities, "test"))));
    }

    private Config confForRoles(String... authorities) {
        return Config.withRoles(authorities);
    }
}
