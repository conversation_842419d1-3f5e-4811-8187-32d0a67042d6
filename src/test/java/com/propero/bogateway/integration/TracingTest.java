package com.propero.bogateway.integration;

import com.c4_soft.springaddons.security.oauth2.test.annotations.WithOAuth2Login;
import com.propero.bogateway.config.AccessPermissions;
import io.micrometer.tracing.Span;
import io.micrometer.tracing.Tracer;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.assertj.core.api.Assertions.assertThat;

@Slf4j
public class TracingTest extends BaseBoGatewayTest {

    @Autowired
    private Tracer tracer;

    @Test
    @WithOAuth2Login(authorities = AccessPermissions.READ_CUST)
    public void shouldLogTraceIdInRequests() {
        log.info("Starting tracing test");

        // Make a request that should generate a trace
        ResponseEntity<Object> response = testRestTemplate.getForEntity(getBaseUrl() + "/customers", Object.class);

        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);

        // Check if we have a current span
        Span currentSpan = tracer.currentSpan();
        if (currentSpan != null) {
            String traceId = currentSpan.context().traceId();
            String spanId = currentSpan.context().spanId();
            log.info("Current trace ID: {}, span ID: {}", traceId, spanId);

            assertThat(traceId).isNotNull().isNotEmpty();
            assertThat(spanId).isNotNull().isNotEmpty();
        }

        // Check MDC for traceId
        String mdcTraceId = MDC.get("traceId");
        String mdcSpanId = MDC.get("spanId");
        log.info("MDC trace ID: {}, span ID: {}", mdcTraceId, mdcSpanId);

        log.info("Tracing test completed");
    }
}
