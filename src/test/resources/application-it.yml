logging:
  level:
    com.propero: DEBUG
    access-logs: OFF # TRACE for access logs between server <-> wiremock
    org.springframework.cloud.gateway: DEBUG # Enable gateway request/response logging
    org.springframework.web.reactive: DEBUG # Enable reactive web logging
    io.micrometer.tracing: DEBUG # Enable tracing logs
  traceable:
    http.mdc-cleaner-disabled: true
    reactive.enabled: true

management:
  tracing:
    sampling:
      probability: 1.0 # Sample 100% of requests for testing
    baggage:
      correlation:
        fields:
          - sessionId
          - customerId
