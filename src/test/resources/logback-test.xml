<configuration scan="true">
    <jmxConfigurator />
    
    <!-- Test-specific logging configuration with traceId -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>%d{HH:mm:ss.SSS} [%thread][%-32.32X{traceId:-}][%-32.32X{spanId:-}] %-5level %logger{36} - %msg%n</Pattern>
        </layout>
    </appender>
    
    <!-- Enable debug logging for tracing components -->
    <logger name="io.micrometer.tracing" level="DEBUG"/>
    <logger name="org.springframework.cloud.gateway" level="DEBUG"/>
    <logger name="org.springframework.web.reactive" level="DEBUG"/>
    <logger name="com.propero" level="DEBUG"/>
    
    <!-- Disable noisy logs -->
    <logger name="access-logs" level="OFF"/>
    <logger name="org.springframework.security" level="INFO"/>
    
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
